using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class Helicopter : MonoBehaviour
{
    [SerializeField] private GameObject helicopterModel;

    [Header("Navigation Settings")]
    [SerializeField] protected List<Transform> waypoints;
    [SerializeField] protected float moveSpeed = 10f;
    [SerializeField] protected float rotationSpeed = 2f;
    [SerializeField] protected float waitTimeAtWaypoint = 1f;
    [SerializeField] protected float landingSpeedFactor = 0.5f;

    public enum HelicopterState
    {
        None,
        InitializingPath,
        Rotating,
        MovingToWaypoint,
        Landing,
        WaitingAtWaypoint,
        PathComplete
    }

    protected HelicopterState currentState = HelicopterState.None;

    // Public property for external systems to monitor helicopter state
    public HelicopterState CurrentState => currentState;

    protected List<Transform> currentPathWaypoints;
    protected int currentWaypointIndex;
    protected Transform activeTargetWaypoint;
    protected float pathSpecificLandingTime;

    private Coroutine _stateMachineExecutionCoroutine;
    private Quaternion _helicopterModelInitialWorldRotation;
    private bool _helicopterModelInitialRotationCaptured = false;

    public virtual void Start()
    {
        if (helicopterModel != null)
        {
            _helicopterModelInitialWorldRotation = helicopterModel.transform.rotation;
            _helicopterModelInitialRotationCaptured = true;
        }
        else
        {
            Debug.LogWarning("Helicopter.cs: helicopterModel is not assigned. Its rotation cannot be preserved.", this);
        }
        TransitionToState(HelicopterState.None);
    }

    public virtual void BeginNavigation(List<Transform> waypointsToFollow, float landingTimeForThisPath)
    {
        if (_stateMachineExecutionCoroutine != null)
        {
            StopCoroutine(_stateMachineExecutionCoroutine);
        }
        this.currentPathWaypoints = waypointsToFollow;
        this.pathSpecificLandingTime = landingTimeForThisPath;
        TransitionToState(HelicopterState.InitializingPath);
        _stateMachineExecutionCoroutine = StartCoroutine(StateMachineDriver());
    }

    protected void TransitionToState(HelicopterState newState)
    {
        currentState = newState;
    }

    private IEnumerator StateMachineDriver()
    {
        while (ShouldContinueStateMachine())
        {
            yield return StartCoroutine(ExecuteCurrentStateAction());
            yield return null; 
        }

        HandleStateMachineCompletion();
    }

    private bool ShouldContinueStateMachine()
    {
        return currentState != HelicopterState.None && currentState != HelicopterState.PathComplete;
    }

    private IEnumerator ExecuteCurrentStateAction()
    {
        switch (currentState)
        {
            case HelicopterState.InitializingPath:
                yield return StartCoroutine(Execute_InitializingPathState());
                break;
            case HelicopterState.Rotating:
                yield return StartCoroutine(Execute_RotatingState());
                break;
            case HelicopterState.MovingToWaypoint:
                yield return StartCoroutine(Execute_MovingToWaypointState());
                break;
            case HelicopterState.Landing:
                yield return StartCoroutine(Execute_LandingState());
                break;
            case HelicopterState.WaitingAtWaypoint:
                yield return StartCoroutine(Execute_WaitingAtWaypointState());
                break;
        }
    }

    private void HandleStateMachineCompletion()
    {
        if (currentState == HelicopterState.PathComplete)
        {
            Debug.Log("Helicopter path successfully completed.", this);
            TransitionToState(HelicopterState.None);
        }
        _stateMachineExecutionCoroutine = null;
    }

    private bool AreWaypointsValid()
    {
        if (currentPathWaypoints == null || currentPathWaypoints.Count == 0)
        {
            Debug.LogError("No waypoints provided for navigation.", this);
            return false;
        }

        for (int i = 0; i < currentPathWaypoints.Count; i++)
        {
            if (currentPathWaypoints[i] == null)
            {
                Debug.LogError($"Waypoint at index {i} is null. Aborting path initialization.", this);
                return false;
            }
        }
        return true;
    }

    protected virtual IEnumerator Execute_InitializingPathState()
    {
        if (!AreWaypointsValid())
        {
            TransitionToState(HelicopterState.None);
            yield break;
        }

        currentWaypointIndex = 0;
        transform.position = currentPathWaypoints[0].position;

        if (currentPathWaypoints.Count == 1)
        {
            Debug.Log("Path has only one waypoint. Helicopter positioned. Path considered complete.", this);
            TransitionToState(HelicopterState.PathComplete);
            yield break;
        }

        activeTargetWaypoint = currentPathWaypoints[currentWaypointIndex + 1];
        TransitionToState(HelicopterState.Rotating);
        yield break;
    }

    private IEnumerator PerformRotation(Transform target)
    {
        Quaternion actualTargetRotationForThisTransform;

        if (helicopterModel != null && helicopterModel.transform == this.transform && _helicopterModelInitialRotationCaptured)
        {
            // This GameObject IS the model. It should not rotate from its initial world rotation.
            actualTargetRotationForThisTransform = _helicopterModelInitialWorldRotation;
        }
        else
        {
            // This GameObject is a controller/parent, or helicopterModel is not specified, or it's a child/other.
            // Calculate the logical rotation for this.transform using enhanced Look-At rotation
            Vector3 directionToTarget = target.position - transform.position;

            if (directionToTarget.sqrMagnitude > 0.001f) // Check if there's a non-zero direction
            {
                // Use LookRotation for more natural helicopter orientation
                // This will make the helicopter face the direction it's moving
                directionToTarget.Normalize();
                Quaternion lookRotation = Quaternion.LookRotation(directionToTarget, Vector3.up);
                actualTargetRotationForThisTransform = lookRotation;
            }
            else
            {
                // Fallback to current rotation if no direction
                actualTargetRotationForThisTransform = transform.rotation;
            }
        }

        // Slerp this.transform towards its actualTargetRotationForThisTransform with improved smoothing
        float rotationThreshold = 2.0f; // Slightly larger threshold for smoother rotation
        while (Quaternion.Angle(transform.rotation, actualTargetRotationForThisTransform) > rotationThreshold)
        {
            float smoothedRotationSpeed = rotationSpeed * Time.deltaTime;
            transform.rotation = Quaternion.Slerp(transform.rotation, actualTargetRotationForThisTransform, smoothedRotationSpeed);

            // Preserve helicopterModel's world rotation if it's defined and captured
            if (_helicopterModelInitialRotationCaptured && helicopterModel != null)
            {
                if (helicopterModel.transform != this.transform && helicopterModel.transform.parent == this.transform)
                {
                    // Model is a child: adjust its localRotation to keep its world rotation constant.
                    helicopterModel.transform.localRotation = Quaternion.Inverse(transform.rotation) * _helicopterModelInitialWorldRotation;
                }
                else if (helicopterModel.transform != this.transform) // Model is not this object and not a child (e.g. unparented or different parent)
                {
                    // Model is a separate object: directly set its world rotation.
                    helicopterModel.transform.rotation = _helicopterModelInitialWorldRotation;
                }
                // If helicopterModel.transform == this.transform, its rotation is already governed by actualTargetRotationForThisTransform.
            }
            yield return null;
        }

        // Smooth final approach instead of snapping
        transform.rotation = Quaternion.Slerp(transform.rotation, actualTargetRotationForThisTransform, 0.5f);

        // Final preservation of helicopterModel's world rotation after this.transform adjustment
        if (_helicopterModelInitialRotationCaptured && helicopterModel != null)
        {
            if (helicopterModel.transform != this.transform && helicopterModel.transform.parent == this.transform)
            {
                helicopterModel.transform.localRotation = Quaternion.Inverse(transform.rotation) * _helicopterModelInitialWorldRotation;
            }
            else if (helicopterModel.transform != this.transform)
            {
                helicopterModel.transform.rotation = _helicopterModelInitialWorldRotation;
            }
        }
    }

    protected virtual IEnumerator Execute_RotatingState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("Rotation state: Target waypoint is null.", this);
            TransitionToState(HelicopterState.None);
            yield break;
        }

        yield return StartCoroutine(PerformRotation(activeTargetWaypoint));

        if (IsApproachingFinalSegment())
        {
            TransitionToState(HelicopterState.Landing);
        }
        else
        {
            TransitionToState(HelicopterState.MovingToWaypoint);
        }
    }

    private bool IsApproachingFinalSegment()
    {
        return currentWaypointIndex + 1 >= currentPathWaypoints.Count - 1;
    }

    private IEnumerator PerformMovement(Transform target)
    {
        while (Vector3.Distance(transform.position, target.position) > 0.1f)
        {
            transform.position = Vector3.MoveTowards(transform.position, target.position, moveSpeed * Time.deltaTime);
            yield return null;
        }
        transform.position = target.position;
    }

    protected virtual IEnumerator Execute_MovingToWaypointState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("Moving state: Target waypoint is null.", this);
            TransitionToState(HelicopterState.None);
            yield break;
        }

        yield return StartCoroutine(PerformMovement(activeTargetWaypoint));
        TransitionToState(HelicopterState.WaitingAtWaypoint);
    }

    private IEnumerator PerformLanding(Transform target, float duration)
    {
        Vector3 initialPosition = transform.position;
        float elapsedTime = 0f;
        float effectiveDuration = Mathf.Max(0.1f, duration);

        while (elapsedTime < effectiveDuration)
        {
            float normalizedTime = elapsedTime / effectiveDuration;
            float smoothedTime = Mathf.SmoothStep(0f, 1f, normalizedTime); // Apply ease-in and ease-out
            transform.position = Vector3.Lerp(initialPosition, target.position, smoothedTime);
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        transform.position = target.position;
    }

    protected virtual IEnumerator Execute_LandingState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("Landing state: Target waypoint is null.", this);
            TransitionToState(HelicopterState.None);
            yield break;
        }
        
        yield return StartCoroutine(PerformLanding(activeTargetWaypoint, pathSpecificLandingTime));
        Debug.Log("Helicopter has landed.", this);
        TransitionToState(HelicopterState.PathComplete);
    }

    private bool HasMoreSegments()
    {
        return currentWaypointIndex + 1 < currentPathWaypoints.Count;
    }

    protected virtual IEnumerator Execute_WaitingAtWaypointState()
    {
        Debug.Log($"Waiting at waypoint: {currentPathWaypoints[currentWaypointIndex +1].name}", this);
        yield return new WaitForSeconds(waitTimeAtWaypoint);

        currentWaypointIndex++;

        if (HasMoreSegments())
        {
            activeTargetWaypoint = currentPathWaypoints[currentWaypointIndex + 1];
            TransitionToState(HelicopterState.Rotating);
        }
        else
        {
            Debug.LogWarning("Waiting state finished, but no more segments. Path might be misconfigured or landing logic was skipped.", this);
            TransitionToState(HelicopterState.PathComplete);
        }
    }
}
