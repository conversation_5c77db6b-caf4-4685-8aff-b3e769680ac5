using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class ForestIntroHelicopter : Helicopter
{
    [Header("Forest Intro Specific Settings")]
    [SerializeField] private float specificLandingTime = 3f;
    [SerializeField] private float autoStartDelay = 0.5f;
    [SerializeField] private bool enableAutoStart = true;
    [SerializeField] private bool enableEnhancedRotation = true;
    [SerializeField] private bool enableDetailedLogging = false;

    [Header("Enhanced Rotation Settings")]
    [SerializeField] private Vector3 helicopterOrientationOffset = new Vector3(-90f, 0f, 0f);
    [SerializeField] private float rotationCompletionThreshold = 5.0f;
    [SerializeField] private float maxRotationTime = 3.0f;

    private Transform currentTargetWaypointTransform;
    private bool hasAutoStarted = false;

    public override void Start()
    {
        base.Start();

        // Validate helicopter orientation settings
        ValidateHelicopterOrientation();

        // Auto-assign waypoints if not already assigned
        if (waypoints == null || waypoints.Count == 0)
        {
            AutoAssignWaypoints();
        }

        if (enableAutoStart && !hasAutoStarted)
        {
            StartCoroutine(AutoStartIntroSequence());
        }
    }

    private void ValidateHelicopterOrientation()
    {
        if (enableDetailedLogging)
        {
            Debug.Log($"ForestIntroHelicopter: Helicopter orientation offset: {helicopterOrientationOffset}", this);
            Debug.Log($"ForestIntroHelicopter: Initial transform rotation: {transform.rotation.eulerAngles}", this);
            Debug.Log($"ForestIntroHelicopter: Enhanced rotation enabled: {enableEnhancedRotation}", this);
        }

        // Warn if the helicopter model reference is not properly set
        if (helicopterModel == null)
        {
            Debug.LogWarning("ForestIntroHelicopter: helicopterModel is not assigned. Enhanced rotation may not work as expected.", this);
        }
        else if (helicopterModel.transform == this.transform)
        {
            if (enableDetailedLogging)
            {
                Debug.Log("ForestIntroHelicopter: Helicopter model is the same as this transform - using enhanced rotation system.", this);
            }
        }
    }

    private void AutoAssignWaypoints()
    {
        // Find the HelicopterWaypoints parent object
        GameObject waypointsParent = GameObject.Find("HelicopterWaypoints");
        if (waypointsParent != null)
        {
            if (waypoints == null)
            {
                waypoints = new List<Transform>();
            }
            else
            {
                waypoints.Clear();
            }

            // Add waypoints in order
            for (int i = 0; i < 4; i++)
            {
                Transform waypoint = waypointsParent.transform.Find($"HelicopterWaypoint_{i}");
                if (waypoint != null)
                {
                    waypoints.Add(waypoint);
                    if (enableDetailedLogging)
                    {
                        Debug.Log($"ForestIntroHelicopter: Auto-assigned waypoint {i}: {waypoint.name} at position {waypoint.position}", this);
                    }
                }
                else
                {
                    Debug.LogWarning($"ForestIntroHelicopter: Could not find HelicopterWaypoint_{i}", this);
                }
            }

            if (enableDetailedLogging)
            {
                Debug.Log($"ForestIntroHelicopter: Auto-assigned {waypoints.Count} waypoints successfully", this);
            }
        }
        else
        {
            Debug.LogError("ForestIntroHelicopter: Could not find HelicopterWaypoints parent object", this);
        }
    }

    private IEnumerator AutoStartIntroSequence()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Starting auto-start sequence", this);
        }

        // Wait for scene stabilization
        yield return new WaitForSeconds(autoStartDelay);

        // Validate waypoints before starting
        if (ValidateWaypoints())
        {
            hasAutoStarted = true;
            StartIntroSequence();

            if (enableDetailedLogging)
            {
                Debug.Log("ForestIntroHelicopter: Auto-start completed successfully", this);
            }
        }
        else
        {
            Debug.LogError("ForestIntroHelicopter: Auto-start failed - waypoints validation failed", this);
        }
    }

    private bool ValidateWaypoints()
    {
        if (waypoints == null || waypoints.Count == 0)
        {
            Debug.LogError("ForestIntroHelicopter: No waypoints assigned", this);
            return false;
        }

        for (int i = 0; i < waypoints.Count; i++)
        {
            if (waypoints[i] == null)
            {
                Debug.LogError($"ForestIntroHelicopter: Waypoint at index {i} is null", this);
                return false;
            }
        }

        if (enableDetailedLogging)
        {
            Debug.Log($"ForestIntroHelicopter: Validated {waypoints.Count} waypoints successfully", this);
        }

        return true;
    }

    public void StartIntroSequence()
    {
        if (this.waypoints == null || this.waypoints.Count == 0)
        {
            Debug.LogError("ForestIntroHelicopter: Cannot start intro sequence - no waypoints", this);
            enabled = false;
            return;
        }

        if (waypoints.Count > 0) {
            currentTargetWaypointTransform = waypoints[0];
        }

        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Starting intro sequence with waypoint navigation", this);
        }

        BeginNavigation(this.waypoints, specificLandingTime);
    }

    public void OnNavigationCompleted()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Navigation completed", this);
        }
    }

    protected void Update()
    {
        // Apply enhanced rotation during both Rotating and MovingToWaypoint states
        if (enableEnhancedRotation &&
            (currentState == HelicopterState.Rotating || currentState == HelicopterState.MovingToWaypoint) &&
            activeTargetWaypoint != null)
        {
            PerformEnhancedRotationDuringFlight();
        }
    }

    private void PerformEnhancedRotationDuringFlight()
    {
        Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;

        // Use full 3D direction for more natural flight behavior
        if (directionToWaypoint.sqrMagnitude > 0.01f)
        {
            directionToWaypoint.Normalize();

            // Calculate target rotation to face the direction of movement
            Quaternion lookRotation = Quaternion.LookRotation(directionToWaypoint, Vector3.up);

            // Apply helicopter-specific rotation correction using configurable offset
            Quaternion helicopterOffset = Quaternion.Euler(helicopterOrientationOffset);
            Quaternion targetRotation = lookRotation * helicopterOffset;

            // Normalize Y-axis rotation to prevent 360° issues
            Vector3 targetEulerAngles = targetRotation.eulerAngles;
            if (targetEulerAngles.y > 180f)
            {
                targetEulerAngles.y -= 360f;
            }
            targetRotation = Quaternion.Euler(targetEulerAngles);

            // Apply smooth rotation interpolation
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime * rotationSpeed);

            if (enableDetailedLogging && Time.frameCount % 60 == 0) // Log every 60 frames to avoid spam
            {
                Debug.Log($"ForestIntroHelicopter: Rotating toward waypoint {activeTargetWaypoint.name} - Target Euler: {targetEulerAngles}", this);
            }
        }
    }

    /// <summary>
    /// Override the base class rotation behavior to use our enhanced rotation system
    /// </summary>
    protected override IEnumerator Execute_RotatingState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("ForestIntroHelicopter: Rotation state - Target waypoint is null.", this);
            TransitionToState(HelicopterState.None);
            yield break;
        }

        if (enableEnhancedRotation)
        {
            // Use our enhanced rotation system instead of the base class rotation
            // The Update method will handle the rotation via PerformEnhancedRotationDuringFlight()

            // Wait for rotation to complete (check angle threshold)
            float rotationStartTime = Time.time;

            while (Time.time - rotationStartTime < maxRotationTime)
            {
                Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;
                if (directionToWaypoint.sqrMagnitude > 0.01f)
                {
                    directionToWaypoint.Normalize();
                    Quaternion lookRotation = Quaternion.LookRotation(directionToWaypoint, Vector3.up);
                    Quaternion helicopterOffset = Quaternion.Euler(helicopterOrientationOffset);
                    Quaternion targetRotation = lookRotation * helicopterOffset;

                    float angleDifference = Quaternion.Angle(transform.rotation, targetRotation);
                    if (angleDifference <= rotationCompletionThreshold)
                    {
                        break; // Rotation complete
                    }
                }
                yield return null;
            }

            if (enableDetailedLogging)
            {
                Debug.Log($"ForestIntroHelicopter: Enhanced rotation completed for waypoint {activeTargetWaypoint.name}", this);
            }
        }
        else
        {
            // Fall back to base class rotation behavior
            yield return StartCoroutine(base.Execute_RotatingState());
            yield break;
        }

        // Transition to next state
        if (IsApproachingFinalSegment())
        {
            TransitionToState(HelicopterState.Landing);
        }
        else
        {
            TransitionToState(HelicopterState.MovingToWaypoint);
        }
    }

    /// <summary>
    /// Helper method to check if approaching final segment (copied from base class)
    /// </summary>
    private bool IsApproachingFinalSegment()
    {
        return currentWaypointIndex + 1 >= currentPathWaypoints.Count - 1;
    }

    protected void OnWaypointReached(Transform reachedWaypoint)
    {
        int reachedWaypointIndex = -1;
        if (this.waypoints != null)
        {
            reachedWaypointIndex = this.waypoints.IndexOf(reachedWaypoint);
        }

        if (reachedWaypointIndex != -1)
        {
            if (enableDetailedLogging)
            {
                Debug.Log($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}' (Index: {reachedWaypointIndex}). Base class will handle progression.", this);
            }
        }
        else
        {
            Debug.LogWarning($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}', but it was not found in the assigned waypoints list. This could indicate a configuration issue.", this);
        }
    }

    /// <summary>
    /// Manual start method for backward compatibility with ForestGameManager
    /// </summary>
    public void ManualStartIntroSequence()
    {
        if (!hasAutoStarted)
        {
            if (enableDetailedLogging)
            {
                Debug.Log("ForestIntroHelicopter: Manual start requested", this);
            }

            hasAutoStarted = true;
            StartIntroSequence();
        }
        else if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Manual start requested but auto-start already completed", this);
        }
    }

    /// <summary>
    /// Force restart the intro sequence (for debugging or special cases)
    /// </summary>
    public void ForceRestartIntroSequence()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Force restart requested", this);
        }

        hasAutoStarted = true;
        StartIntroSequence();
    }

    /// <summary>
    /// Public method to manually assign waypoints (for testing and debugging)
    /// </summary>
    [ContextMenu("Assign Waypoints")]
    public void ManuallyAssignWaypoints()
    {
        AutoAssignWaypoints();
        if (enableDetailedLogging)
        {
            Debug.Log($"ForestIntroHelicopter: Manually assigned waypoints. Count: {(waypoints != null ? waypoints.Count : 0)}", this);
        }
    }

    /// <summary>
    /// Test method to validate helicopter orientation (for debugging)
    /// </summary>
    [ContextMenu("Test Helicopter Orientation")]
    public void TestHelicopterOrientation()
    {
        Debug.Log("=== ForestIntroHelicopter Orientation Test ===", this);
        Debug.Log($"Current Transform Rotation: {transform.rotation.eulerAngles}", this);
        Debug.Log($"Helicopter Orientation Offset: {helicopterOrientationOffset}", this);
        Debug.Log($"Enhanced Rotation Enabled: {enableEnhancedRotation}", this);
        Debug.Log($"Rotation Speed: {rotationSpeed}", this);
        Debug.Log($"Rotation Completion Threshold: {rotationCompletionThreshold}°", this);
        Debug.Log($"Max Rotation Time: {maxRotationTime}s", this);

        if (activeTargetWaypoint != null)
        {
            Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;
            if (directionToWaypoint.sqrMagnitude > 0.01f)
            {
                directionToWaypoint.Normalize();
                Quaternion lookRotation = Quaternion.LookRotation(directionToWaypoint, Vector3.up);
                Quaternion helicopterOffset = Quaternion.Euler(helicopterOrientationOffset);
                Quaternion targetRotation = lookRotation * helicopterOffset;

                Debug.Log($"Direction to Waypoint: {directionToWaypoint}", this);
                Debug.Log($"Look Rotation: {lookRotation.eulerAngles}", this);
                Debug.Log($"Target Rotation: {targetRotation.eulerAngles}", this);
                Debug.Log($"Angle Difference: {Quaternion.Angle(transform.rotation, targetRotation)}°", this);
            }
        }
        else
        {
            Debug.Log("No active target waypoint for orientation test", this);
        }
        Debug.Log("=== End Orientation Test ===", this);
    }
}
